import pandas as pd
import os
from typing import List, Dict, Any, Optional
from datetime import datetime
from database_manager import DatabaseManager


class DataExporter:
    """
    Verileri CSV, Excel veya SQLite formatında dışa aktaran sınıf
    """

    def __init__(self, output_dir: str = "exports"):
        """
        Args:
            output_dir: Dışa aktarılan dosyaların kaydedileceği dizin
        """
        self.output_dir = output_dir
        self.db_manager = DatabaseManager()

        # Dizin yoksa oluştur
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)

    def export_autocomplete_data(self,
                                data: List[str],
                                keyword: str,
                                platform: str,
                                file_format: str = "csv") -> str:
        """
        Autocomplete verilerini dışa aktarır

        Args:
            data: Autocomplete önerileri listesi
            keyword: Aranan anahtar kelime
            platform: Platform adı (youtube, google vb.)
            file_format: <PERSON>sya formatı ("csv" veya "excel")

        Returns:
            <PERSON><PERSON>ilen dos<PERSON>ın yolu
        """
        # Dosya adı oluştur
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{platform}_{keyword.replace(' ', '_')}_{timestamp}"

        # DataFrame oluştur
        df = pd.DataFrame(data, columns=["Öneri"])

        # Dosyayı kaydet
        if file_format.lower() == "csv":
            file_path = os.path.join(self.output_dir, f"{filename}.csv")
            df.to_csv(file_path, index=False, encoding="utf-8-sig")  # utf-8-sig for Excel compatibility
        else:  # excel
            file_path = os.path.join(self.output_dir, f"{filename}.xlsx")
            df.to_excel(file_path, index=False, sheet_name="Öneriler")

        return file_path

    def export_trends_data(self,
                          data: pd.DataFrame,
                          keyword: str,
                          data_type: str = "interest_over_time",
                          file_format: str = "csv") -> str:
        """
        Google Trends verilerini dışa aktarır

        Args:
            data: Trends verileri DataFrame olarak
            keyword: Aranan anahtar kelime
            data_type: Veri tipi (interest_over_time, related_queries vb.)
            file_format: Dosya formatı ("csv" veya "excel")

        Returns:
            Kaydedilen dosyanın yolu
        """
        if data is None or (isinstance(data, pd.DataFrame) and data.empty):
            return ""

        # Dosya adı oluştur
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"trends_{data_type}_{keyword.replace(' ', '_')}_{timestamp}"

        # Dosyayı kaydet
        if file_format.lower() == "csv":
            file_path = os.path.join(self.output_dir, f"{filename}.csv")
            data.to_csv(file_path, encoding="utf-8-sig")  # utf-8-sig for Excel compatibility
        else:  # excel
            file_path = os.path.join(self.output_dir, f"{filename}.xlsx")
            data.to_excel(file_path, sheet_name="Trends Verileri")

        return file_path

    def export_combined_data(self,
                            autocomplete_data: List[str],
                            trends_data: Optional[pd.DataFrame],
                            keyword: str,
                            platform: str,
                            country: str,
                            language: str,
                            file_format: str = "csv") -> str:
        """
        Autocomplete ve Trends verilerini birleştirip dışa aktarır

        Args:
            autocomplete_data: Autocomplete önerileri listesi
            trends_data: Trends verileri DataFrame olarak
            keyword: Aranan anahtar kelime
            platform: Platform adı (youtube, google vb.)
            country: Ülke kodu
            language: Dil kodu
            file_format: Dosya formatı ("csv", "excel" veya "sqlite")

        Returns:
            Kaydedilen dosyanın yolu veya SQLite için arama ID'si
        """
        # SQLite'a kaydet
        search_id = self.db_manager.save_search(keyword, platform, country, language)
        self.db_manager.save_autocomplete_results(search_id, autocomplete_data)
        if trends_data is not None and not trends_data.empty:
            self.db_manager.save_trends_results(search_id, trends_data)

        # Dosya formatı SQLite ise, arama ID'sini döndür
        if file_format.lower() == "sqlite":
            return str(search_id)
        # Dosya adı oluştur
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{platform}_combined_{keyword.replace(' ', '_')}_{timestamp}"

        # Autocomplete DataFrame'i oluştur
        autocomplete_df = pd.DataFrame(autocomplete_data, columns=["Öneri"])

        # Dosyayı kaydet
        if file_format.lower() == "csv":
            file_path = os.path.join(self.output_dir, f"{filename}.csv")

            # Autocomplete verilerini kaydet
            autocomplete_df.to_csv(file_path, index=False, encoding="utf-8-sig")

            # Trends verileri varsa, aynı dosyaya ekle
            if trends_data is not None and not trends_data.empty:
                with open(file_path, 'a', encoding="utf-8-sig") as f:
                    f.write("\n\nGoogle Trends Verileri\n")
                trends_data.to_csv(file_path, mode='a', encoding="utf-8-sig")

        else:  # excel
            file_path = os.path.join(self.output_dir, f"{filename}.xlsx")

            # Excel writer oluştur
            with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                # Autocomplete verilerini kaydet
                autocomplete_df.to_excel(writer, sheet_name="Öneriler", index=False)

                # Trends verileri varsa, ayrı bir sayfaya kaydet
                if trends_data is not None and not trends_data.empty:
                    trends_data.to_excel(writer, sheet_name="Trends Verileri")

        return file_path

    def load_from_database(self, search_id: int) -> Dict[str, Any]:
        """
        Veritabanından belirli bir aramanın verilerini yükler

        Args:
            search_id: Arama ID'si

        Returns:
            Arama verileri sözlük olarak
        """
        # Autocomplete sonuçlarını al
        autocomplete_results = self.db_manager.get_autocomplete_results(search_id)

        # Trends verilerini al
        trends_data = self.db_manager.get_trends_results(search_id)

        return {
            "autocomplete_results": autocomplete_results,
            "trends_data": trends_data
        }
