import tkinter as tk
from gui import NicheFinderGUI
import sys
import os


def main():
    """
    Ana uygulama fonksiyonu
    """
    # Tkinter root penceresi oluştur
    root = tk.Tk()
    
    # Uygulama simges<PERSON> (e<PERSON>er varsa)
    try:
        if getattr(sys, 'frozen', False):
            # PyInstaller ile paketlenmiş uygulama
            application_path = sys._MEIPASS
        else:
            # Normal Python betiği
            application_path = os.path.dirname(os.path.abspath(__file__))
        
        icon_path = os.path.join(application_path, "assets", "icon.ico")
        if os.path.exists(icon_path):
            root.iconbitmap(icon_path)
    except Exception:
        pass  # Simge yüklenemezse devam et
    
    # GUI'yi başlat
    app = NicheFinderGUI(root)
    
    # Uygulama döngüsünü başlat
    root.mainloop()


if __name__ == "__main__":
    main()
