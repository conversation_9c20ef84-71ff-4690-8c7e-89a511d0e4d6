import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import threading
import queue
import pandas as pd
from typing import List, Dict, Any, Optional, Callable
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import os
import time
import threading
import sqlite3
from database_manager import DatabaseManager


class NicheFinderGUI:
    """
    Niche Finder uygulamasının GUI arayüzü
    """

    def __init__(self, root):
        """
        Args:
            root: Tkinter root penceresi
        """
        self.root = root
        self.root.title("Niche Finder - YouTube ve SEO Anahtar Kelime Bulucu")
        self.root.geometry("1000x700")
        self.root.minsize(900, 650)

        # Veritabanı yöneticisi
        self.db_manager = DatabaseManager()

        # Tema ve stil ayarları
        self.style = ttk.Style()
        self.style.theme_use('clam')  # 'clam', 'alt', 'default', 'classic'

        # Modern renkler
        self.primary_color = "#3498db"  # Mavi
        self.secondary_color = "#e74c3c"  # Kırmızı
        self.accent_color = "#2ecc71"  # Yeşil
        self.bg_color = "#f5f5f5"  # Açık gri
        self.text_color = "#2c3e50"  # Koyu lacivert
        self.card_bg = "#ffffff"  # Beyaz (kartlar için)

        # Modern stil ayarları
        self.style.configure('TFrame', background=self.bg_color)
        self.style.configure('TLabel', background=self.bg_color, foreground=self.text_color, font=('Segoe UI', 10))

        # Modern buton stili
        self.style.configure('TButton',
                          font=('Segoe UI', 10, 'bold'),
                          background=self.primary_color,
                          foreground='white',
                          padding=6,
                          relief='flat')
        self.style.map('TButton',
                    background=[('active', self.primary_color), ('pressed', '#2980b9')],
                    relief=[('pressed', 'flat'), ('!pressed', 'flat')])

        # Accent buton stili (yeşil butonlar için)
        self.style.configure('Accent.TButton',
                          font=('Segoe UI', 10, 'bold'),
                          background=self.accent_color,
                          foreground='white',
                          padding=6)
        self.style.map('Accent.TButton',
                    background=[('active', self.accent_color), ('pressed', '#27ae60')],
                    relief=[('pressed', 'flat'), ('!pressed', 'flat')])

        # Tehlike buton stili (kırmızı butonlar için)
        self.style.configure('Danger.TButton',
                          font=('Segoe UI', 10, 'bold'),
                          background=self.secondary_color,
                          foreground='white',
                          padding=6)
        self.style.map('Danger.TButton',
                    background=[('active', self.secondary_color), ('pressed', '#c0392b')],
                    relief=[('pressed', 'flat'), ('!pressed', 'flat')])

        # Diğer widget'lar için stiller
        self.style.configure('TEntry', font=('Segoe UI', 10), padding=6, relief='flat')
        self.style.configure('TCombobox', font=('Segoe UI', 10), padding=4)
        self.style.configure('Heading.TLabel', font=('Segoe UI', 16, 'bold'), foreground=self.primary_color)

        # Kart stili (beyaz arka planlı paneller için)
        self.style.configure('Card.TFrame', background=self.card_bg, relief='flat', borderwidth=0)
        self.style.configure('Card.TLabel', background=self.card_bg, foreground=self.text_color, font=('Segoe UI', 10))

        # Notebook stili
        self.style.configure('TNotebook', background=self.bg_color, borderwidth=0)
        self.style.configure('TNotebook.Tab', background=self.bg_color, foreground=self.text_color, padding=[10, 4], font=('Segoe UI', 10))
        self.style.map('TNotebook.Tab',
                    background=[('selected', self.card_bg), ('active', '#e1e1e1')],
                    foreground=[('selected', self.primary_color), ('active', self.text_color)])

        # Root pencere ayarları
        self.root.configure(bg=self.bg_color)

        # Mesaj kuyruğu (thread'ler arası iletişim için)
        self.message_queue = queue.Queue()

        # Arama durumu
        self.search_running = False
        self.stop_requested = False
        self.search_thread = None

        # Sonuçlar
        self.autocomplete_results = []
        self.trends_data = None

        # Mevcut arama ID'si için varsayılan değer
        self.current_search_id = None

        # GUI bileşenlerini oluştur
        self.create_widgets()

        # Mesaj kuyruğunu periyodik olarak kontrol et
        self.check_message_queue()

    def create_widgets(self):
        """GUI bileşenlerini oluşturur"""
        # Ana çerçeve
        main_frame = ttk.Frame(self.root, padding=15)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # Başlık ve logo alanı
        header_frame = ttk.Frame(main_frame, style='Card.TFrame', padding=10)
        header_frame.pack(fill=tk.X, pady=(0, 15))

        title_label = ttk.Label(header_frame, text="Niche Finder", style='Heading.TLabel')
        title_label.pack(side=tk.LEFT)

        subtitle_label = ttk.Label(header_frame, text="YouTube ve SEO Anahtar Kelime Bulucu",
                                 style='Card.TLabel', font=('Segoe UI', 10, 'italic'))
        subtitle_label.pack(side=tk.LEFT, padx=(10, 0))

        # Giriş bölümü - Kart stili ile
        input_frame = ttk.LabelFrame(main_frame, text="Arama Parametreleri", padding=15, style='Card.TFrame')
        input_frame.pack(fill=tk.X, pady=10)

        # Anahtar kelime - modern görünüm
        keyword_frame = ttk.Frame(input_frame, style='Card.TFrame')
        keyword_frame.pack(fill=tk.X, pady=8)

        keyword_label = ttk.Label(keyword_frame, text="Anahtar Kelime Kökü:", style='Card.TLabel', font=('Segoe UI', 10, 'bold'))
        keyword_label.pack(side=tk.LEFT, padx=(0, 10))

        self.keyword_entry = ttk.Entry(keyword_frame, width=40, font=('Segoe UI', 11))
        self.keyword_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10), ipady=3)
        self.keyword_entry.insert(0, "bedava uc")  # Örnek değer

        # Platform ve ülke seçimi - modern görünüm
        platform_frame = ttk.Frame(input_frame, style='Card.TFrame')
        platform_frame.pack(fill=tk.X, pady=8)

        # Platform seçimi
        platform_label = ttk.Label(platform_frame, text="Platform:", style='Card.TLabel', font=('Segoe UI', 10, 'bold'))
        platform_label.pack(side=tk.LEFT, padx=(0, 10))

        self.platform_var = tk.StringVar(value="YouTube")
        platform_options = ["YouTube", "Google", "Amazon"]
        platform_combobox = ttk.Combobox(platform_frame, textvariable=self.platform_var, values=platform_options,
                                       state="readonly", width=15, font=('Segoe UI', 10))
        platform_combobox.pack(side=tk.LEFT, padx=(0, 20))

        # Ülke seçimi
        country_label = ttk.Label(platform_frame, text="Ülke:", style='Card.TLabel', font=('Segoe UI', 10, 'bold'))
        country_label.pack(side=tk.LEFT, padx=(0, 10))

        self.country_var = tk.StringVar(value="tr")
        country_options = ["tr", "us", "uk", "de", "fr", "es", "it", "jp"]
        country_combobox = ttk.Combobox(platform_frame, textvariable=self.country_var, values=country_options, state="readonly", width=10)
        country_combobox.pack(side=tk.LEFT)

        # Dil seçimi
        language_label = ttk.Label(platform_frame, text="Dil:")
        language_label.pack(side=tk.LEFT, padx=(20, 5))

        self.language_var = tk.StringVar(value="tr")
        language_options = ["tr", "en", "de", "fr", "es", "it", "ja"]
        language_combobox = ttk.Combobox(platform_frame, textvariable=self.language_var, values=language_options, state="readonly", width=10)
        language_combobox.pack(side=tk.LEFT)

        # Dışa aktarma formatı - modern görünüm
        export_frame = ttk.Frame(input_frame, style='Card.TFrame')
        export_frame.pack(fill=tk.X, pady=8)

        export_label = ttk.Label(export_frame, text="Dışa Aktarma Formatı:", style='Card.TLabel', font=('Segoe UI', 10, 'bold'))
        export_label.pack(side=tk.LEFT, padx=(0, 10))

        self.export_format_var = tk.StringVar(value="sqlite")

        # SQLite seçeneği
        sqlite_radio = ttk.Radiobutton(export_frame, text="SQLite", variable=self.export_format_var, value="sqlite", style='Card.TLabel')
        sqlite_radio.pack(side=tk.LEFT, padx=(0, 10))

        # CSV seçeneği
        csv_radio = ttk.Radiobutton(export_frame, text="CSV", variable=self.export_format_var, value="csv", style='Card.TLabel')
        csv_radio.pack(side=tk.LEFT, padx=(0, 10))

        # Excel seçeneği
        excel_radio = ttk.Radiobutton(export_frame, text="Excel", variable=self.export_format_var, value="excel", style='Card.TLabel')
        excel_radio.pack(side=tk.LEFT)

        # Google Trends seçenekleri - modern görünüm
        trends_frame = ttk.Frame(input_frame, style='Card.TFrame')
        trends_frame.pack(fill=tk.X, pady=8)

        self.include_trends_var = tk.BooleanVar(value=True)
        trends_check = ttk.Checkbutton(trends_frame, text="Google Trends Verilerini Dahil Et",
                                     variable=self.include_trends_var, style='Card.TLabel')
        trends_check.pack(side=tk.LEFT, padx=(0, 15))

        timeframe_label = ttk.Label(trends_frame, text="Zaman Aralığı:", style='Card.TLabel', font=('Segoe UI', 10, 'bold'))
        timeframe_label.pack(side=tk.LEFT, padx=(0, 10))

        self.timeframe_var = tk.StringVar(value="today 3-m")
        timeframe_options = ["today 1-m", "today 3-m", "today 12-m", "today 5-y"]
        timeframe_combobox = ttk.Combobox(trends_frame, textvariable=self.timeframe_var,
                                        values=timeframe_options, state="readonly", width=15, font=('Segoe UI', 10))
        timeframe_combobox.pack(side=tk.LEFT)

        # Butonlar - modern görünüm
        button_frame = ttk.Frame(input_frame, style='Card.TFrame')
        button_frame.pack(fill=tk.X, pady=(15, 5))

        # Arama butonu - mavi
        self.search_button = ttk.Button(button_frame, text="Arama Yap", command=self.start_search, style='TButton')
        self.search_button.pack(side=tk.LEFT, padx=(0, 10))

        # Arama Yap+ butonu - kırmızı (sadece ana kelime için)
        self.quick_search_button = ttk.Button(button_frame, text="Arama Yap +",
                                          command=self.start_quick_search, style='Danger.TButton')
        self.quick_search_button.pack(side=tk.LEFT, padx=(0, 10))

        # Durdur butonu - kırmızı (aramayı durdurmak için)
        self.stop_button = ttk.Button(button_frame, text="Durdur",
                                   command=self.stop_search, style='Danger.TButton',
                                   state=tk.DISABLED)
        self.stop_button.pack(side=tk.LEFT, padx=(0, 10))

        # Dışa aktarma butonu - yeşil
        self.export_button = ttk.Button(button_frame, text="Dışa Aktar", command=self.export_data,
                                      state=tk.DISABLED, style='Accent.TButton')
        self.export_button.pack(side=tk.LEFT, padx=(0, 10))

        # Geçmiş butonu - yeni eklenen
        self.history_button = ttk.Button(button_frame, text="Arama Geçmişi", command=self.show_history)
        self.history_button.pack(side=tk.LEFT)

        # Sonuçlar bölümü - modern görünüm
        results_frame = ttk.LabelFrame(main_frame, text="Sonuçlar", padding=15, style='Card.TFrame')
        results_frame.pack(fill=tk.BOTH, expand=True, pady=15)

        # Notebook (sekmeli arayüz)
        self.notebook = ttk.Notebook(results_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Autocomplete sonuçları sekmesi
        self.autocomplete_tab = ttk.Frame(self.notebook)
        self.notebook.add(self.autocomplete_tab, text="Arama Önerileri")

        # Autocomplete sonuçları listesi
        autocomplete_frame = ttk.Frame(self.autocomplete_tab)
        autocomplete_frame.pack(fill=tk.BOTH, expand=True)

        # Scrollbar
        scrollbar = ttk.Scrollbar(autocomplete_frame)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Treeview (tablo görünümü)
        self.results_tree = ttk.Treeview(autocomplete_frame, columns=("Öneri", "Favori"), show="headings", yscrollcommand=scrollbar.set)
        self.results_tree.heading("Öneri", text="Öneri")
        self.results_tree.heading("Favori", text="Favori")
        self.results_tree.column("Öneri", width=400)
        self.results_tree.column("Favori", width=50)
        self.results_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # Çift tıklama ile favori işaretleme
        self.results_tree.bind("<Double-1>", self.toggle_favorite)

        scrollbar.config(command=self.results_tree.yview)

        # Google Trends sekmesi
        self.trends_tab = ttk.Frame(self.notebook)
        self.notebook.add(self.trends_tab, text="Google Trends")

        # Trends grafik çerçevesi
        self.trends_graph_frame = ttk.Frame(self.trends_tab)
        self.trends_graph_frame.pack(fill=tk.BOTH, expand=True)

        # Arama Geçmişi sekmesi
        self.history_tab = ttk.Frame(self.notebook)
        self.notebook.add(self.history_tab, text="Arama Geçmişi")

        # Favoriler sekmesi
        self.favorites_tab = ttk.Frame(self.notebook)
        self.notebook.add(self.favorites_tab, text="Favoriler")

        # Geçmiş sekmesini oluştur
        self.create_history_tab()

        # Favoriler sekmesini oluştur
        self.create_favorites_tab()

        # Durum çubuğu
        self.status_var = tk.StringVar(value="Hazır")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, relief=tk.SUNKEN, anchor=tk.W)
        status_bar.pack(fill=tk.X, side=tk.BOTTOM, pady=(5, 0))

    def start_search(self):
        """Tam arama işlemini başlatır (ana kelime + tüm harf varyasyonları)"""
        keyword = self.keyword_entry.get().strip()
        if not keyword:
            messagebox.showerror("Hata", "Lütfen bir anahtar kelime girin.")
            return

        # Arayüzü devre dışı bırak
        self.search_button.config(state=tk.DISABLED)
        self.quick_search_button.config(state=tk.DISABLED)
        self.export_button.config(state=tk.DISABLED)
        self.status_var.set("Tam arama yapılıyor...")

        # Önceki sonuçları temizle
        for item in self.results_tree.get_children():
            self.results_tree.delete(item)

        # Trends grafiğini temizle
        for widget in self.trends_graph_frame.winfo_children():
            widget.destroy()

        # Arama parametrelerini al
        platform = self.platform_var.get().lower()
        country = self.country_var.get().lower()
        language = self.language_var.get().lower()
        include_trends = self.include_trends_var.get()
        timeframe = self.timeframe_var.get()

        # Arama durumunu ayarla
        self.search_running = True
        self.stop_requested = False

        # Durdur butonunu etkinleştir
        self.stop_button.config(state=tk.NORMAL)

        # Arama işlemini ayrı bir thread'de başlat (full_search=True)
        try:
            print(f"Tam arama başlatılıyor: {keyword}, {platform}, {country}, {language}")
            self.search_thread_obj = threading.Thread(
                target=self.perform_search,
                args=(keyword, platform, country, language, include_trends, timeframe, True)
            )
            self.search_thread_obj.daemon = True
            self.search_thread_obj.start()
            print("Tam arama thread'i başlatıldı")
        except Exception as e:
            print(f"Tam arama başlatılırken hata: {e}")
            self.status_var.set(f"Hata: {e}")
            self.search_button.config(state=tk.NORMAL)
            self.quick_search_button.config(state=tk.NORMAL)
            self.stop_button.config(state=tk.DISABLED)

    def start_quick_search(self):
        """Hızlı arama işlemini başlatır (sadece ana kelime için)"""
        keyword = self.keyword_entry.get().strip()
        if not keyword:
            messagebox.showerror("Hata", "Lütfen bir anahtar kelime girin.")
            return

        # Arayüzü devre dışı bırak
        self.search_button.config(state=tk.DISABLED)
        self.quick_search_button.config(state=tk.DISABLED)
        self.export_button.config(state=tk.DISABLED)
        self.status_var.set("Hızlı arama yapılıyor...")

        # Önceki sonuçları temizle
        for item in self.results_tree.get_children():
            self.results_tree.delete(item)

        # Trends grafiğini temizle
        for widget in self.trends_graph_frame.winfo_children():
            widget.destroy()

        # Arama parametrelerini al
        platform = self.platform_var.get().lower()
        country = self.country_var.get().lower()
        language = self.language_var.get().lower()
        include_trends = self.include_trends_var.get()
        timeframe = self.timeframe_var.get()

        # Arama durumunu ayarla
        self.search_running = True
        self.stop_requested = False

        # Durdur butonunu etkinleştir
        self.stop_button.config(state=tk.NORMAL)

        # Arama işlemini ayrı bir thread'de başlat (full_search=False)
        try:
            print(f"Hızlı arama başlatılıyor: {keyword}, {platform}, {country}, {language}")
            self.search_thread_obj = threading.Thread(
                target=self.perform_search,
                args=(keyword, platform, country, language, include_trends, timeframe, False)
            )
            self.search_thread_obj.daemon = True
            self.search_thread_obj.start()
            print("Hızlı arama thread'i başlatıldı")
        except Exception as e:
            print(f"Hızlı arama başlatılırken hata: {e}")
            self.status_var.set(f"Hata: {e}")
            self.search_button.config(state=tk.NORMAL)
            self.quick_search_button.config(state=tk.NORMAL)
            self.stop_button.config(state=tk.DISABLED)

    def stop_search(self):
        """Devam eden aramayı durdurur"""
        if self.search_running and hasattr(self, 'search_thread_obj') and self.search_thread_obj.is_alive():
            # Durdurma isteği gönder
            self.stop_requested = True
            self.status_var.set("Arama durduruldu.")

            # Butonları güncelle
            self.stop_button.config(state=tk.DISABLED)
            self.search_button.config(state=tk.NORMAL)
            self.quick_search_button.config(state=tk.NORMAL)

    def perform_search(self, keyword, platform, country, language, include_trends, timeframe, full_search=True):
        """
        Arama işlemini gerçekleştiren thread

        Args:
            keyword: Anahtar kelime
            platform: Platform adı
            country: Ülke kodu
            language: Dil kodu
            include_trends: Google Trends verisi dahil edilsin mi
            timeframe: Zaman aralığı
            full_search: Tam arama yapılsın mı (True) yoksa sadece ana kelime için mi (False)
        """
        try:
            print(f"Arama thread'i başladı: {keyword}, {platform}, {country}, {language}, full_search={full_search}")

            # Autocomplete verilerini çek
            from autocomplete_scraper import AutocompleteScraper
            scraper = AutocompleteScraper()

            if full_search:
                self.message_queue.put(("status", "Autocomplete verileri çekiliyor (tüm harfler için)..."))
            else:
                self.message_queue.put(("status", "Autocomplete verileri çekiliyor (sadece ana kelime)..."))

            # Durum güncellemesi için callback fonksiyonu
            def status_update(message):
                print(f"Durum güncellemesi: {message}")
                self.message_queue.put(("status", message))
                # Durdurma isteği kontrolü
                return not self.stop_requested

            print(f"Autocomplete varyasyonları çekiliyor: {keyword}, {platform}, {country}, {language}, full_search={full_search}")
            self.autocomplete_results = scraper.get_autocomplete_variations(
                keyword, platform, country, language, full_search, status_update
            )
            print(f"Bulunan sonuçlar: {len(self.autocomplete_results) if self.autocomplete_results else 0} adet")

            # Eğer durdurma isteği varsa işlemi sonlandır
            if self.stop_requested:
                self.message_queue.put(("status", "Arama kullanıcı tarafından durduruldu."))
                self.message_queue.put(("complete", None))
                self.search_running = False
                return

            # Sonuçları GUI'ye gönder
            print(f"Bulunan sonuçlar: {self.autocomplete_results}")
            if self.autocomplete_results:
                self.message_queue.put(("autocomplete", self.autocomplete_results))
            else:
                self.message_queue.put(("status", "Hiç sonuç bulunamadı."))

            # Google Trends verilerini çek
            if include_trends and not self.stop_requested:
                self.message_queue.put(("status", "Google Trends verileri çekiliyor..."))

                from trends_analyzer import TrendsAnalyzer
                analyzer = TrendsAnalyzer(hl=language)

                # En fazla 5 anahtar kelime ile trends verisi çek
                trends_keywords = [keyword]
                if self.autocomplete_results:
                    # İlk 4 öneriyi ekle (toplam 5 anahtar kelime olacak)
                    for suggestion in self.autocomplete_results[:4]:
                        if suggestion not in trends_keywords:
                            trends_keywords.append(suggestion)

                self.trends_data = analyzer.get_interest_over_time(trends_keywords, timeframe)

                # Trends verilerini GUI'ye gönder
                self.message_queue.put(("trends", self.trends_data))

            # Verileri veritabanına kaydet ve arama ID'sini al
            if not self.stop_requested:
                from data_exporter import DataExporter
                exporter = DataExporter()
                search_id = exporter.export_combined_data(
                    self.autocomplete_results,
                    self.trends_data,
                    keyword,
                    platform,
                    country,
                    language,
                    "sqlite"
                )
                # Arama ID'sini kaydet
                self.current_search_id = int(search_id)
                self.message_queue.put(("status", f"Tamamlandı! Arama ID: {search_id}"))
            else:
                self.message_queue.put(("status", "Tamamlandı!"))

            self.message_queue.put(("complete", None))
            self.search_running = False

        except Exception as e:
            print(f"Arama thread'inde hata: {e}")
            import traceback
            traceback.print_exc()
            self.message_queue.put(("error", str(e)))

    def check_message_queue(self):
        """Mesaj kuyruğunu kontrol eder ve GUI'yi günceller"""
        try:
            while True:
                message_type, message = self.message_queue.get_nowait()

                if message_type == "status":
                    self.status_var.set(message)

                elif message_type == "autocomplete":
                    # Autocomplete sonuçlarını tabloya ekle
                    try:
                        # Önce mevcut sonuçları temizle
                        for item in self.results_tree.get_children():
                            self.results_tree.delete(item)

                        # Yeni sonuçları ekle
                        print(f"GUI'ye eklenecek sonuçlar: {message}")
                        for suggestion in message:
                            # Favori durumunu kontrol et
                            is_favorite = self.db_manager.is_favorite(self.current_search_id, suggestion) if hasattr(self, 'current_search_id') else False
                            favorite_mark = "★" if is_favorite else ""
                            self.results_tree.insert("", tk.END, values=(suggestion, favorite_mark))

                        # Notebook'u Arama Önerileri sekmesine getir
                        self.notebook.select(self.autocomplete_tab)
                    except Exception as e:
                        print(f"Sonuçlar tabloya eklenirken hata: {e}")

                elif message_type == "trends":
                    # Trends grafiğini oluştur
                    if message is not None and not message.empty:
                        self.create_trends_graph(message)

                elif message_type == "complete":
                    # Arama tamamlandı, arayüzü etkinleştir
                    self.search_button.config(state=tk.NORMAL)
                    self.quick_search_button.config(state=tk.NORMAL)
                    self.stop_button.config(state=tk.DISABLED)
                    if self.autocomplete_results:
                        self.export_button.config(state=tk.NORMAL)

                elif message_type == "error":
                    # Hata mesajı göster
                    messagebox.showerror("Hata", f"Bir hata oluştu: {message}")
                    self.status_var.set("Hata oluştu")
                    self.search_button.config(state=tk.NORMAL)
                    self.quick_search_button.config(state=tk.NORMAL)
                    self.stop_button.config(state=tk.DISABLED)
                    self.search_running = False

                self.message_queue.task_done()

        except queue.Empty:
            # Kuyruk boş, 100ms sonra tekrar kontrol et
            self.root.after(100, self.check_message_queue)

    def create_trends_graph(self, data):
        """
        Google Trends verilerinden grafik oluşturur

        Args:
            data: Trends verileri DataFrame olarak
        """
        # Önceki grafiği temizle
        for widget in self.trends_graph_frame.winfo_children():
            widget.destroy()

        # Matplotlib figürü oluştur
        fig, ax = plt.subplots(figsize=(10, 6))

        # Verileri çiz
        for column in data.columns:
            if column != 'isPartial':
                ax.plot(data.index, data[column], label=column, linewidth=2)

        # Grafik ayarları
        ax.set_title("Google Trends - Zaman İçinde İlgi", fontsize=14)
        ax.set_xlabel("Tarih", fontsize=12)
        ax.set_ylabel("İlgi (0-100)", fontsize=12)
        ax.grid(True, linestyle='--', alpha=0.7)
        ax.legend()

        # Figürü Tkinter'a ekle
        canvas = FigureCanvasTkAgg(fig, master=self.trends_graph_frame)
        canvas.draw()
        canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)

    def export_data(self):
        """Verileri dışa aktarır"""
        if not self.autocomplete_results:
            messagebox.showinfo("Bilgi", "Dışa aktarılacak veri bulunamadı.")
            return

        try:
            from data_exporter import DataExporter
            exporter = DataExporter()

            keyword = self.keyword_entry.get().strip()
            platform = self.platform_var.get().lower()
            country = self.country_var.get().lower()
            language = self.language_var.get().lower()
            file_format = self.export_format_var.get()

            # Autocomplete ve Trends verilerini birlikte dışa aktar
            file_path = exporter.export_combined_data(
                self.autocomplete_results,
                self.trends_data,
                keyword,
                platform,
                country,
                language,
                file_format
            )

            if file_format.lower() == "sqlite":
                # Arama ID'sini kaydet
                self.current_search_id = int(file_path)
                self.status_var.set(f"Veriler veritabanına kaydedildi. ID: {file_path}")
                messagebox.showinfo("Başarılı", f"Veriler başarıyla veritabanına kaydedildi.\nArama ID: {file_path}")
            elif file_path:
                self.status_var.set(f"Veriler dışa aktarıldı: {file_path}")
                messagebox.showinfo("Başarılı", f"Veriler başarıyla dışa aktarıldı:\n{file_path}")

                # Dosyayı göster
                os.startfile(os.path.dirname(file_path))
            else:
                messagebox.showerror("Hata", "Dışa aktarma sırasında bir hata oluştu.")

        except Exception as e:
            messagebox.showerror("Hata", f"Dışa aktarma sırasında bir hata oluştu: {e}")

    def show_history(self):
        """Arama geçmişini gösterir"""
        try:
            # Arama geçmişini al
            search_history = self.db_manager.get_search_history(limit=20)

            if not search_history:
                messagebox.showinfo("Bilgi", "Henüz kaydedilmiş arama bulunamadı.")
                return

            # Geçmiş penceresi oluştur
            history_window = tk.Toplevel(self.root)
            history_window.title("Arama Geçmişi")
            history_window.geometry("600x400")
            history_window.minsize(500, 300)
            history_window.configure(bg=self.bg_color)

            # Başlık
            title_label = ttk.Label(history_window, text="Arama Geçmişi", style='Heading.TLabel')
            title_label.pack(pady=(10, 15))

            # Geçmiş listesi için frame
            list_frame = ttk.Frame(history_window, style='Card.TFrame', padding=10)
            list_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

            # Scrollbar
            scrollbar = ttk.Scrollbar(list_frame)
            scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

            # Treeview (tablo görünümü)
            columns = ("id", "keyword", "platform", "country", "date")
            history_tree = ttk.Treeview(list_frame, columns=columns, show="headings", yscrollcommand=scrollbar.set)

            # Sütun başlıkları
            history_tree.heading("id", text="ID")
            history_tree.heading("keyword", text="Anahtar Kelime")
            history_tree.heading("platform", text="Platform")
            history_tree.heading("country", text="Ülke")
            history_tree.heading("date", text="Tarih")

            # Sütun genişlikleri
            history_tree.column("id", width=50)
            history_tree.column("keyword", width=150)
            history_tree.column("platform", width=100)
            history_tree.column("country", width=80)
            history_tree.column("date", width=150)

            history_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            scrollbar.config(command=history_tree.yview)

            # Geçmiş verilerini ekle
            for item in search_history:
                history_tree.insert("", tk.END, values=(
                    item["id"],
                    item["keyword"],
                    item["platform"],
                    item["country"],
                    item["search_date"]
                ))

            # Çift tıklama olayı
            def on_item_double_click(event):
                item = history_tree.selection()[0]
                search_id = history_tree.item(item, "values")[0]
                self.load_search_results(search_id)
                history_window.destroy()

            history_tree.bind("<Double-1>", on_item_double_click)

            # Butonlar için frame
            button_frame = ttk.Frame(history_window, style='Card.TFrame', padding=5)
            button_frame.pack(fill=tk.X, padx=10, pady=(0, 10))

            # Yükle butonu
            def load_selected():
                selection = history_tree.selection()
                if selection:
                    item = selection[0]
                    search_id = history_tree.item(item, "values")[0]
                    self.load_search_results(search_id)
                    history_window.destroy()
                else:
                    messagebox.showinfo("Bilgi", "Lütfen bir arama seçin.")

            load_button = ttk.Button(button_frame, text="Seçili Aramayı Yükle", command=load_selected, style='Accent.TButton')
            load_button.pack(side=tk.LEFT, padx=5)

            # Sil butonu
            def delete_selected():
                selection = history_tree.selection()
                if selection:
                    item = selection[0]
                    search_id = history_tree.item(item, "values")[0]
                    if messagebox.askyesno("Onay", "Seçili aramayı silmek istediğinize emin misiniz?"):
                        if self.db_manager.delete_search(search_id):
                            history_tree.delete(item)
                            messagebox.showinfo("Bilgi", "Arama başarıyla silindi.")
                        else:
                            messagebox.showerror("Hata", "Arama silinirken bir hata oluştu.")
                else:
                    messagebox.showinfo("Bilgi", "Lütfen bir arama seçin.")

            delete_button = ttk.Button(button_frame, text="Seçili Aramayı Sil", command=delete_selected, style='Danger.TButton')
            delete_button.pack(side=tk.LEFT, padx=5)

            # Kapat butonu
            close_button = ttk.Button(button_frame, text="Kapat", command=history_window.destroy)
            close_button.pack(side=tk.RIGHT, padx=5)

        except Exception as e:
            messagebox.showerror("Hata", f"Arama geçmişi gösterilirken bir hata oluştu: {e}")

    def load_search_results(self, search_id):
        """Veritabanından belirli bir aramanın sonuçlarını yükler"""
        try:
            from data_exporter import DataExporter
            exporter = DataExporter()

            # Verileri yükle
            data = exporter.load_from_database(search_id)

            # Sonuçları güncelle
            self.autocomplete_results = data["autocomplete_results"]
            self.trends_data = data["trends_data"]

            # Mevcut arama ID'sini kaydet
            self.current_search_id = int(search_id)

            # Arayüzü güncelle
            self.update_results_display()

            # Dışa aktarma butonunu etkinleştir
            self.export_button.config(state=tk.NORMAL)

            # Durum mesajını güncelle
            self.status_var.set(f"Arama #{search_id} yüklendi.")

            # Arama Önerileri sekmesine geç
            self.notebook.select(self.autocomplete_tab)

        except Exception as e:
            messagebox.showerror("Hata", f"Arama sonuçları yüklenirken bir hata oluştu: {e}")

    def update_results_display(self):
        """Sonuç ekranını günceller"""
        # Autocomplete sonuçlarını güncelle
        for item in self.results_tree.get_children():
            self.results_tree.delete(item)

        for suggestion in self.autocomplete_results:
            # Favori durumunu kontrol et
            is_favorite = self.db_manager.is_favorite(self.current_search_id, suggestion) if hasattr(self, 'current_search_id') else False
            favorite_mark = "★" if is_favorite else ""
            self.results_tree.insert("", tk.END, values=(suggestion, favorite_mark))

        # Trends grafiklerini güncelle
        if self.trends_data is not None and not self.trends_data.empty:
            self.create_trends_graph(self.trends_data)

        # Geçmiş sekmesini güncelle
        self.update_history_tab()

        # Favoriler sekmesini güncelle
        self.update_favorites_tab()

    def create_history_tab(self):
        """Arama geçmişi sekmesini oluşturur"""
        # Ana frame - PanedWindow kullanarak bölünmüş görünüm oluştur
        self.history_paned = ttk.PanedWindow(self.history_tab, orient=tk.HORIZONTAL)
        self.history_paned.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Sol panel - Arama geçmişi listesi
        left_frame = ttk.Frame(self.history_paned, style='Card.TFrame', padding=10)
        self.history_paned.add(left_frame, weight=1)

        # Başlık
        history_label = ttk.Label(left_frame, text="Son Aramalar", style='Card.TLabel', font=('Segoe UI', 12, 'bold'))
        history_label.pack(side=tk.TOP, anchor=tk.W, pady=(0, 10))

        # Arama geçmişi listesi için frame
        list_container = ttk.Frame(left_frame, style='Card.TFrame')
        list_container.pack(fill=tk.BOTH, expand=True)

        # Scrollbar
        scrollbar = ttk.Scrollbar(list_container)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Listbox
        self.history_listbox = tk.Listbox(list_container, yscrollcommand=scrollbar.set,
                                        font=('Segoe UI', 10), selectmode=tk.SINGLE,
                                        activestyle='dotbox', bg=self.card_bg, fg=self.text_color,
                                        highlightthickness=0, bd=0)
        self.history_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.config(command=self.history_listbox.yview)

        # Listbox seçim olayı
        self.history_listbox.bind('<<ListboxSelect>>', self.on_history_select)

        # Kopyalama için kısayol
        self.history_listbox.bind('<Control-c>', self.copy_selected_history)

        # Sağ panel - Seçilen aramanın sonuçları
        right_frame = ttk.Frame(self.history_paned, style='Card.TFrame', padding=10)
        self.history_paned.add(right_frame, weight=2)

        # Başlık
        self.results_title = ttk.Label(right_frame, text="Sonuçlar", style='Card.TLabel', font=('Segoe UI', 12, 'bold'))
        self.results_title.pack(side=tk.TOP, anchor=tk.W, pady=(0, 10))

        # Sonuçlar için frame
        results_container = ttk.Frame(right_frame, style='Card.TFrame')
        results_container.pack(fill=tk.BOTH, expand=True)

        # Scrollbar
        results_scrollbar = ttk.Scrollbar(results_container)
        results_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Sonuç listesi
        self.results_listbox = tk.Listbox(results_container, yscrollcommand=results_scrollbar.set,
                                        font=('Segoe UI', 10), selectmode=tk.EXTENDED,
                                        activestyle='dotbox', bg=self.card_bg, fg=self.text_color,
                                        highlightthickness=0, bd=0)
        self.results_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        results_scrollbar.config(command=self.results_listbox.yview)

        # Kopyalama için kısayol
        self.results_listbox.bind('<Control-c>', self.copy_selected_results)

        # Çift tıklama ile favori ekleme
        self.results_listbox.bind('<Double-1>', self.add_history_result_to_favorites)

        # Butonlar için frame
        button_frame = ttk.Frame(right_frame, style='Card.TFrame', padding=5)
        button_frame.pack(fill=tk.X, side=tk.BOTTOM, pady=(10, 0))

        # Tümünü kopyala butonu
        copy_all_button = ttk.Button(button_frame, text="Tümünü Kopyala",
                                   command=self.copy_all_results, style='TButton')
        copy_all_button.pack(side=tk.LEFT, padx=(0, 5))

        # Seçilenleri kopyala butonu
        copy_selected_button = ttk.Button(button_frame, text="Seçilenleri Kopyala",
                                       command=lambda: self.copy_selected_results(None), style='TButton')
        copy_selected_button.pack(side=tk.LEFT, padx=(0, 5))

        # Sil butonu
        delete_button = ttk.Button(button_frame, text="Sil",
                                command=self.delete_current_history_item, style='Danger.TButton')
        delete_button.pack(side=tk.LEFT)

        # Yenile butonu
        refresh_button = ttk.Button(button_frame, text="Yenile",
                                  command=self.update_history_tab, style='Accent.TButton')
        refresh_button.pack(side=tk.RIGHT)

        # Geçmiş listesini doldur
        self.update_history_tab()

    def update_history_tab(self):
        """Arama geçmişi sekmesini günceller"""
        try:
            # Arama geçmişini al
            search_history = self.db_manager.get_search_history(limit=50)

            # Listbox'u temizle
            self.history_listbox.delete(0, tk.END)

            # Geçmiş verilerini ekle
            self.history_data = search_history  # Referans için sakla

            for item in search_history:
                display_text = f"{item['id']} - {item['keyword']} ({item['platform']}, {item['country']}) - {item['search_date']}"
                self.history_listbox.insert(tk.END, display_text)

            # Renklendirme
            for i in range(0, self.history_listbox.size(), 2):
                self.history_listbox.itemconfig(i, bg='#f0f0f0')

        except Exception as e:
            print(f"Geçmiş sekmesi güncellenirken hata: {e}")

    def on_history_select(self, event):
        """Geçmiş listesinden bir öğe seçildiğinde çağrılır"""
        try:
            # Seçilen öğeyi al
            selection = self.history_listbox.curselection()
            if not selection:
                return

            index = selection[0]
            item = self.history_data[index]
            search_id = item['id']

            # Mevcut arama ID'sini kaydet
            self.current_search_id = int(search_id)

            # Başlığı güncelle
            self.results_title.config(text=f"Sonuçlar: {item['keyword']} ({item['platform']})")

            # Verileri yükle
            from data_exporter import DataExporter
            exporter = DataExporter()
            data = exporter.load_from_database(search_id)

            # Sonuçları göster
            self.results_listbox.delete(0, tk.END)

            for suggestion in data["autocomplete_results"]:
                self.results_listbox.insert(tk.END, suggestion)

            # Renklendirme
            for i in range(0, self.results_listbox.size(), 2):
                self.results_listbox.itemconfig(i, bg='#f0f0f0')

            # Durum mesajını güncelle
            self.status_var.set(f"Arama #{search_id} seçildi.")

        except Exception as e:
            print(f"Geçmiş öğesi seçilirken hata: {e}")

    def copy_selected_history(self, event):
        """Seçili geçmiş öğelerini panoya kopyalar"""
        selection = self.history_listbox.curselection()
        if not selection:
            return

        selected_text = self.history_listbox.get(selection[0])
        self.root.clipboard_clear()
        self.root.clipboard_append(selected_text)
        self.status_var.set("Seçili geçmiş öğesi panoya kopyalandı.")

    def copy_selected_results(self, event):
        """Seçili sonuçları panoya kopyalar"""
        selection = self.results_listbox.curselection()
        if not selection:
            messagebox.showinfo("Bilgi", "Lütfen kopyalanacak sonuçları seçin.")
            return

        selected_text = "\n".join([self.results_listbox.get(i) for i in selection])
        self.root.clipboard_clear()
        self.root.clipboard_append(selected_text)
        self.status_var.set(f"{len(selection)} sonuç panoya kopyalandı.")

    def copy_all_results(self):
        """Tüm sonuçları panoya kopyalar"""
        if self.results_listbox.size() == 0:
            messagebox.showinfo("Bilgi", "Kopyalanacak sonuç bulunamadı.")
            return

        all_text = "\n".join([self.results_listbox.get(i) for i in range(self.results_listbox.size())])
        self.root.clipboard_clear()
        self.root.clipboard_append(all_text)
        self.status_var.set(f"{self.results_listbox.size()} sonuç panoya kopyalandı.")

    def delete_current_history_item(self):
        """Geçmiş sekmesinde görüntülenen mevcut aramayı siler"""
        # Seçili geçmiş öğesini al
        selection = self.history_listbox.curselection()
        if not selection:
            messagebox.showinfo("Bilgi", "Lütfen silmek için bir arama seçin.")
            return

        index = selection[0]
        item_text = self.history_listbox.get(index)

        # ID'yi al (format: "1 - bedava (youtube, tr) - 2023-05-20 12:34:56")
        try:
            search_id = int(item_text.split(' - ')[0])
        except (ValueError, IndexError):
            messagebox.showerror("Hata", "Arama ID'si alınamadı.")
            return

        # Silme işlemini onayla
        if not messagebox.askyesno("Onay", f"#{search_id} ID'li aramayı silmek istediğinize emin misiniz?"):
            return

        # Veritabanından sil
        if self.db_manager.delete_search(search_id):
            # Listbox'tan kaldır
            self.history_listbox.delete(index)

            # Sonuçları temizle
            self.results_listbox.delete(0, tk.END)
            self.results_title.config(text="Sonuçlar")

            messagebox.showinfo("Bilgi", f"#{search_id} ID'li arama başarıyla silindi.")
            self.status_var.set(f"Arama #{search_id} silindi.")
        else:
            messagebox.showerror("Hata", "Arama silinirken bir hata oluştu.")

    def toggle_favorite(self, event):
        """Seçili öneriyi favori olarak işaretle veya işareti kaldır"""
        try:
            # Seçili öğeyi al
            selection = self.results_tree.selection()
            if not selection:
                return

            item = selection[0]
            suggestion = self.results_tree.item(item, "values")[0]

            # Mevcut arama ID'si yoksa uyarı ver
            if not hasattr(self, 'current_search_id'):
                # Arama yaptıktan sonra ID'yi kaydetmek için export_data metodunu çağır
                self.export_data()
                if not hasattr(self, 'current_search_id'):
                    messagebox.showinfo("Bilgi", "Lütfen önce bir arama yapın veya geçmişten bir arama yükleyin.")
                    return

            # Favori durumunu kontrol et ve değiştir
            is_favorite = self.db_manager.is_favorite(self.current_search_id, suggestion)

            if is_favorite:
                # Favorilerden kaldır
                # Önce favori ID'sini bul
                conn = sqlite3.connect(self.db_manager.db_path)
                cursor = conn.cursor()
                cursor.execute('''
                SELECT id FROM favorites
                WHERE search_id = ? AND suggestion = ?
                ''', (self.current_search_id, suggestion))

                row = cursor.fetchone()
                conn.close()

                if row:
                    favorite_id = row[0]
                    if self.db_manager.remove_from_favorites(favorite_id):
                        # Treeview'i güncelle
                        self.results_tree.item(item, values=(suggestion, ""))
                        self.status_var.set(f"\"{suggestion}\" favorilerden kaldırıldı.")
                        # Favoriler sekmesini güncelle
                        self.update_favorites_tab()
                        # Favoriler sekmesine geç
                        self.notebook.select(self.favorites_tab)
            else:
                # Favorilere ekle
                if self.db_manager.add_to_favorites(self.current_search_id, suggestion):
                    # Treeview'i güncelle
                    self.results_tree.item(item, values=(suggestion, "★"))
                    self.status_var.set(f"\"{suggestion}\" favorilere eklendi.")
                    # Favoriler sekmesini güncelle
                    self.update_favorites_tab()
                    # Favoriler sekmesine geç
                    self.notebook.select(self.favorites_tab)
        except Exception as e:
            print(f"Favori durumu değiştirilirken hata: {e}")
            import traceback
            traceback.print_exc()

    def create_favorites_tab(self):
        """Favoriler sekmesini oluşturur"""
        # Ana frame
        favorites_frame = ttk.Frame(self.favorites_tab, style='Card.TFrame', padding=10)
        favorites_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

        # Başlık
        favorites_label = ttk.Label(favorites_frame, text="Favori Arama Sonuçları", style='Card.TLabel', font=('Segoe UI', 12, 'bold'))
        favorites_label.pack(side=tk.TOP, anchor=tk.W, pady=(0, 10))

        # Favoriler listesi için frame
        list_container = ttk.Frame(favorites_frame, style='Card.TFrame')
        list_container.pack(fill=tk.BOTH, expand=True)

        # Scrollbar
        scrollbar = ttk.Scrollbar(list_container)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # Treeview (tablo görünümü)
        columns = ("suggestion", "keyword", "platform", "country")
        self.favorites_tree = ttk.Treeview(list_container, columns=columns, show="headings", yscrollcommand=scrollbar.set)

        # Sütun başlıkları
        self.favorites_tree.heading("suggestion", text="Öneri")
        self.favorites_tree.heading("keyword", text="Anahtar Kelime")
        self.favorites_tree.heading("platform", text="Platform")
        self.favorites_tree.heading("country", text="Ülke")

        # Sütun genişlikleri
        self.favorites_tree.column("suggestion", width=300)
        self.favorites_tree.column("keyword", width=150)
        self.favorites_tree.column("platform", width=100)
        self.favorites_tree.column("country", width=80)

        self.favorites_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.config(command=self.favorites_tree.yview)

        # Çift tıklama ile favorilerden kaldırma
        self.favorites_tree.bind("<Double-1>", self.remove_favorite_from_list)

        # Butonlar için frame
        button_frame = ttk.Frame(favorites_frame, style='Card.TFrame', padding=5)
        button_frame.pack(fill=tk.X, side=tk.BOTTOM, pady=(10, 0))

        # Tümünü kopyala butonu
        copy_all_button = ttk.Button(button_frame, text="Tümünü Kopyala",
                                   command=self.copy_all_favorites, style='TButton')
        copy_all_button.pack(side=tk.LEFT, padx=(0, 5))

        # Seçilenleri kopyala butonu
        copy_selected_button = ttk.Button(button_frame, text="Seçilenleri Kopyala",
                                       command=self.copy_selected_favorites, style='TButton')
        copy_selected_button.pack(side=tk.LEFT, padx=(0, 5))

        # Yenile butonu
        refresh_button = ttk.Button(button_frame, text="Yenile",
                                  command=self.update_favorites_tab, style='Accent.TButton')
        refresh_button.pack(side=tk.RIGHT)

        # Favoriler listesini doldur
        self.update_favorites_tab()

    def update_favorites_tab(self):
        """Favoriler sekmesini günceller"""
        try:
            # Favorileri al
            favorites = self.db_manager.get_favorites()

            # Treeview'i temizle
            if hasattr(self, 'favorites_tree'):
                for item in self.favorites_tree.get_children():
                    self.favorites_tree.delete(item)

                # Favorileri ekle
                for item in favorites:
                    self.favorites_tree.insert("", tk.END, values=(
                        item["suggestion"],
                        item["keyword"],
                        item["platform"],
                        item["country"]
                    ), tags=(str(item["id"]),))

                # Renklendirme
                for i, item in enumerate(self.favorites_tree.get_children()):
                    if i % 2 == 0:
                        self.favorites_tree.item(item, tags=(self.favorites_tree.item(item, "tags")[0], "even"))

                self.favorites_tree.tag_configure("even", background="#f0f0f0")
        except Exception as e:
            print(f"Favoriler sekmesi güncellenirken hata: {e}")

    def remove_favorite_from_list(self, event):
        """Favoriler listesinden bir öğeyi kaldır"""
        try:
            # Seçili öğeyi al
            selection = self.favorites_tree.selection()
            if not selection:
                return

            item = selection[0]
            favorite_id = int(self.favorites_tree.item(item, "tags")[0])
            suggestion = self.favorites_tree.item(item, "values")[0]

            # Onay iste
            if not messagebox.askyesno("Onay", f"\"{suggestion}\" favorilerden kaldırılsın mı?"):
                return

            # Favorilerden kaldır
            if self.db_manager.remove_from_favorites(favorite_id):
                # Listeden kaldır
                self.favorites_tree.delete(item)
                self.status_var.set(f"\"{suggestion}\" favorilerden kaldırıldı.")

                # Ana sonuç listesini güncelle (eğer görünüyorsa)
                for result_item in self.results_tree.get_children():
                    if self.results_tree.item(result_item, "values")[0] == suggestion:
                        self.results_tree.item(result_item, values=(suggestion, ""))
                        break
        except Exception as e:
            print(f"Favori kaldırılırken hata: {e}")

    def copy_all_favorites(self):
        """Tüm favorileri panoya kopyalar"""
        if not hasattr(self, 'favorites_tree') or len(self.favorites_tree.get_children()) == 0:
            messagebox.showinfo("Bilgi", "Kopyalanacak favori yok.")
            return

        # Tüm favorileri al
        all_favorites = [self.favorites_tree.item(item, "values")[0] for item in self.favorites_tree.get_children()]
        all_text = "\n".join(all_favorites)

        # Panoya kopyala
        self.root.clipboard_clear()
        self.root.clipboard_append(all_text)
        self.status_var.set("Tüm favoriler panoya kopyalandı.")

    def copy_selected_favorites(self):
        """Seçili favorileri panoya kopyalar"""
        if not hasattr(self, 'favorites_tree'):
            return

        selection = self.favorites_tree.selection()
        if not selection:
            messagebox.showinfo("Bilgi", "Lütfen kopyalamak için en az bir favori seçin.")
            return

        # Seçili favorileri al
        selected_favorites = [self.favorites_tree.item(item, "values")[0] for item in selection]
        selected_text = "\n".join(selected_favorites)

        # Panoya kopyala
        self.root.clipboard_clear()
        self.root.clipboard_append(selected_text)
        self.status_var.set("Seçili favoriler panoya kopyalandı.")

    def add_history_result_to_favorites(self, event):
        """Arama geçmişi sonuçlarından bir öğeyi favorilere ekler"""
        try:
            # Seçili öğeyi al
            selection = self.results_listbox.curselection()
            if not selection:
                return

            index = selection[0]
            suggestion = self.results_listbox.get(index)

            # Mevcut arama ID'si yoksa, geçmiş listesinden seçili öğenin ID'sini al
            if not hasattr(self, 'current_search_id'):
                history_selection = self.history_listbox.curselection()
                if not history_selection:
                    messagebox.showinfo("Bilgi", "Lütfen önce bir arama seçin.")
                    return

                history_index = history_selection[0]
                item = self.history_data[history_index]
                self.current_search_id = item['id']

                # Başlığı güncelle
                self.results_title.config(text=f"Sonuçlar: {item['keyword']} ({item['platform']})")

                # Durum mesajını güncelle
                self.status_var.set(f"Arama #{self.current_search_id} seçildi.")

            # Favori durumunu kontrol et
            is_favorite = self.db_manager.is_favorite(self.current_search_id, suggestion)

            if is_favorite:
                # Favorilerden kaldır
                conn = sqlite3.connect(self.db_manager.db_path)
                cursor = conn.cursor()
                cursor.execute('''
                SELECT id FROM favorites
                WHERE search_id = ? AND suggestion = ?
                ''', (self.current_search_id, suggestion))

                row = cursor.fetchone()
                conn.close()

                if row:
                    favorite_id = row[0]
                    if self.db_manager.remove_from_favorites(favorite_id):
                        self.status_var.set(f"\"{suggestion}\" favorilerden kaldırıldı.")
                        # Favoriler sekmesini güncelle
                        self.update_favorites_tab()
                        # Favoriler sekmesine geç
                        self.notebook.select(self.favorites_tab)
            else:
                # Favorilere ekle
                if self.db_manager.add_to_favorites(self.current_search_id, suggestion):
                    self.status_var.set(f"\"{suggestion}\" favorilere eklendi.")
                    # Favoriler sekmesini güncelle
                    self.update_favorites_tab()
                    # Favoriler sekmesine geç
                    self.notebook.select(self.favorites_tab)
        except Exception as e:
            print(f"Geçmiş sonuçlarından favori eklenirken hata: {e}")
            import traceback
            traceback.print_exc()
