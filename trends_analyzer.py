from pytrends.request import TrendReq
import pandas as pd
from typing import List, Dict, Any, Optional
import time


class TrendsAnalyzer:
    """
    Google Trends verilerini çeken ve analiz eden sınıf
    """
    
    def __init__(self, hl: str = 'tr', tz: int = 180):
        """
        Args:
            hl: <PERSON>l kodu (tr, en vb.)
            tz: <PERSON><PERSON> di<PERSON> (Türkiye için 180)
        """
        self.pytrends = TrendReq(hl=hl, tz=tz)
        self.delay = 1  # İstekler arası gecikme (saniye)
    
    def get_interest_over_time(self, keywords: List[str], timeframe: str = 'today 12-m') -> Optional[pd.DataFrame]:
        """
        Belirli anahtar kelimeler için zaman içindeki ilgiyi çeker
        
        Args:
            keywords: <PERSON><PERSON><PERSON> kelimeler listesi (en fazla 5 anahtar kelime)
            timeframe: <PERSON>aman aralığı ('today 1-m', 'today 3-m', 'today 12-m', 'today 5-y' vb.)
            
        Returns:
            Zaman içindeki ilgi verileri DataFrame olarak
        """
        if not keywords:
            return None
        
        # Google Trends en fazla 5 anahtar kelime kabul eder
        if len(keywords) > 5:
            keywords = keywords[:5]
        
        try:
            self.pytrends.build_payload(keywords, cat=0, timeframe=timeframe)
            time.sleep(self.delay)  # API sınırlamalarını aşmamak için gecikme
            
            data = self.pytrends.interest_over_time()
            if data.empty:
                print("Trends verisi bulunamadı")
                return None
                
            return data
        except Exception as e:
            print(f"Trends verisi çekilirken hata oluştu: {e}")
            return None
    
    def get_related_queries(self, keyword: str, timeframe: str = 'today 12-m') -> Dict[str, pd.DataFrame]:
        """
        Bir anahtar kelime için ilgili sorguları çeker
        
        Args:
            keyword: Anahtar kelime
            timeframe: Zaman aralığı
            
        Returns:
            İlgili sorgular (top ve rising) DataFrame olarak
        """
        try:
            self.pytrends.build_payload([keyword], cat=0, timeframe=timeframe)
            time.sleep(self.delay)
            
            related_queries = self.pytrends.related_queries()
            return related_queries.get(keyword, {})
        except Exception as e:
            print(f"İlgili sorgular çekilirken hata oluştu: {e}")
            return {}
    
    def get_related_topics(self, keyword: str, timeframe: str = 'today 12-m') -> Dict[str, pd.DataFrame]:
        """
        Bir anahtar kelime için ilgili konuları çeker
        
        Args:
            keyword: Anahtar kelime
            timeframe: Zaman aralığı
            
        Returns:
            İlgili konular (top ve rising) DataFrame olarak
        """
        try:
            self.pytrends.build_payload([keyword], cat=0, timeframe=timeframe)
            time.sleep(self.delay)
            
            related_topics = self.pytrends.related_topics()
            return related_topics.get(keyword, {})
        except Exception as e:
            print(f"İlgili konular çekilirken hata oluştu: {e}")
            return {}
    
    def get_interest_by_region(self, keyword: str, country: str = 'TR', timeframe: str = 'today 12-m') -> Optional[pd.DataFrame]:
        """
        Bir anahtar kelime için bölgesel ilgiyi çeker
        
        Args:
            keyword: Anahtar kelime
            country: Ülke kodu (TR, US vb.)
            timeframe: Zaman aralığı
            
        Returns:
            Bölgesel ilgi verileri DataFrame olarak
        """
        try:
            self.pytrends.build_payload([keyword], cat=0, timeframe=timeframe, geo=country)
            time.sleep(self.delay)
            
            data = self.pytrends.interest_by_region(resolution='COUNTRY', inc_low_vol=True, inc_geo_code=True)
            if data.empty:
                print("Bölgesel veri bulunamadı")
                return None
                
            return data
        except Exception as e:
            print(f"Bölgesel veri çekilirken hata oluştu: {e}")
            return None
    
    def get_trending_searches(self, country: str = 'turkey') -> Optional[pd.DataFrame]:
        """
        Günlük trend olan aramaları çeker
        
        Args:
            country: Ülke adı (turkey, united_states vb.)
            
        Returns:
            Trend olan aramalar DataFrame olarak
        """
        try:
            data = self.pytrends.trending_searches(pn=country)
            return data
        except Exception as e:
            print(f"Trend aramalar çekilirken hata oluştu: {e}")
            return None
    
    def get_keyword_suggestions(self, keyword: str) -> List[Dict[str, str]]:
        """
        Bir anahtar kelime için Google'ın önerdiği anahtar kelimeleri çeker
        
        Args:
            keyword: Anahtar kelime
            
        Returns:
            Önerilen anahtar kelimeler listesi
        """
        try:
            suggestions = self.pytrends.suggestions(keyword)
            return suggestions
        except Exception as e:
            print(f"Anahtar kelime önerileri çekilirken hata oluştu: {e}")
            return []
