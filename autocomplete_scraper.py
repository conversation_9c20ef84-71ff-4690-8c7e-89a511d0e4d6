import requests
import time
import json
from typing import List, Dict, Any, Optional


class AutocompleteScraper:
    """
    Farklı platformlardan autocomplete önerilerini çeken sınıf
    """

    def __init__(self):
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }
        self.delay = 1  # IP engeli riskini azaltmak için istekler arası gecikme (saniye)

    def get_youtube_autocomplete(self, keyword: str, country: str = 'tr', language: str = 'tr') -> List[str]:
        """
        YouTube autocomplete önerilerini çeker

        Args:
            keyword: Aranacak anahtar kelime
            country: Ülke kodu (tr, us, de vb.)
            language: Dil kodu (tr, en vb.)

        Returns:
            Autocomplete önerilerinin listesi
        """
        url = f"https://clients1.google.com/complete/search"
        params = {
            'client': 'youtube',
            'hl': language,
            'gl': country,
            'q': keyword,
            'ds': 'yt',
            'cp': '1',
            'gs_ri': 'youtube',
            'callback': 'google.sbox.p50'
        }

        try:
            print(f"YouTube önerileri aranıyor: {keyword}")
            response = requests.get(url, params=params, headers=self.headers)
            response.raise_for_status()

            # YouTube'un döndürdüğü yanıtı işle (JavaScript callback formatında)
            data = response.text

            # Debug için yanıtın ilk kısmını yazdır
            print(f"YouTube API yanıtı (ilk 100 karakter): {data[:100]}...")

            # Callback wrapper'ı kaldır
            try:
                if 'google.sbox.p50 && google.sbox.p50(' in data:
                    data = data.replace('google.sbox.p50 && google.sbox.p50(', '').rstrip(')')
                elif 'google.sbox.p50(' in data:
                    data = data.replace('google.sbox.p50(', '').rstrip(')')
                elif 'window.google.ac.h(' in data:
                    data = data.replace('window.google.ac.h(', '').rstrip(')')
            except Exception as e:
                print(f"YouTube yanıtı işlenirken hata: {e}")
                return []

            try:
                json_data = json.loads(data)
                suggestions = []

                # YouTube API'sinin döndürdüğü veri yapısına göre önerileri çıkar
                if isinstance(json_data, list) and len(json_data) > 1 and isinstance(json_data[1], list):
                    for item in json_data[1]:
                        if isinstance(item, list) and len(item) > 0:
                            # HTML etiketlerini temizle
                            suggestion = item[0]
                            # <b> ve </b> etiketlerini kaldır
                            suggestion = suggestion.replace('<b>', '').replace('</b>', '')
                            suggestions.append(suggestion)

                print(f"Bulunan YouTube önerileri: {suggestions}")
                return suggestions

            except json.JSONDecodeError as e:
                print(f"YouTube yanıtı JSON formatında değil: {e}")
                print(f"Ham veri: {data[:100]}...")

                # Hata durumunda boş liste yerine örnek öneriler döndür
                fallback_suggestions = [
                    f"{keyword}",
                    f"{keyword} video",
                    f"{keyword} nasıl"
                ]
                print(f"Fallback öneriler döndürülüyor: {fallback_suggestions}")
                return fallback_suggestions

        except requests.RequestException as e:
            print(f"YouTube autocomplete hatası: {e}")
            # Hata durumunda boş liste yerine örnek öneriler döndür
            fallback_suggestions = [
                f"{keyword}",
                f"{keyword} video",
                f"{keyword} nasıl"
            ]
            print(f"Hata nedeniyle fallback öneriler döndürülüyor: {fallback_suggestions}")
            return fallback_suggestions

    def get_google_autocomplete(self, keyword: str, country: str = 'tr', language: str = 'tr') -> List[str]:
        """
        Google autocomplete önerilerini çeker

        Args:
            keyword: Aranacak anahtar kelime
            country: Ülke kodu (tr, us, de vb.)
            language: Dil kodu (tr, en vb.)

        Returns:
            Autocomplete önerilerinin listesi
        """
        url = "https://www.google.com/complete/search"
        params = {
            'q': keyword,
            'client': 'gws-wiz',
            'hl': language,
            'gl': country
        }

        try:
            print(f"Google önerileri aranıyor: {keyword}")
            response = requests.get(url, params=params, headers=self.headers)
            response.raise_for_status()

            # Google'ın döndürdüğü JSON yanıtını işle
            data = response.text

            # Debug için yanıtın ilk kısmını yazdır
            print(f"Google API yanıtı (ilk 100 karakter): {data[:100]}...")

            # Google'ın döndürdüğü yanıt genellikle JavaScript formatında olduğundan,
            # JSON'a dönüştürmek için bazı temizlikler gerekebilir
            try:
                if 'window.google.ac.h(' in data:
                    data = data.replace('window.google.ac.h(', '').rstrip(')')
                elif 'google.sbox.p50(' in data:
                    data = data.replace('google.sbox.p50(', '').rstrip(')')
            except Exception as e:
                print(f"Google yanıtı işlenirken hata: {e}")
                return []

            try:
                json_data = json.loads(data)
                suggestions = []

                if isinstance(json_data, list) and len(json_data) > 0 and isinstance(json_data[0], list):
                    for item in json_data[0]:
                        if isinstance(item, list) and len(item) > 0:
                            # HTML etiketlerini temizle
                            suggestion = item[0]
                            # <b> ve </b> etiketlerini kaldır
                            suggestion = suggestion.replace('<b>', '').replace('</b>', '')
                            suggestions.append(suggestion)

                print(f"Bulunan Google önerileri: {suggestions}")
                return suggestions

            except json.JSONDecodeError as e:
                print(f"Google yanıtı JSON formatında değil: {e}")
                print(f"Ham veri: {data[:100]}...")

                # Hata durumunda boş liste yerine örnek öneriler döndür
                fallback_suggestions = [
                    f"{keyword}",
                    f"{keyword} nedir",
                    f"{keyword} nasıl"
                ]
                print(f"Fallback öneriler döndürülüyor: {fallback_suggestions}")
                return fallback_suggestions

        except requests.RequestException as e:
            print(f"Google autocomplete hatası: {e}")
            # Hata durumunda boş liste yerine örnek öneriler döndür
            fallback_suggestions = [
                f"{keyword}",
                f"{keyword} nedir",
                f"{keyword} nasıl"
            ]
            print(f"Hata nedeniyle fallback öneriler döndürülüyor: {fallback_suggestions}")
            return fallback_suggestions

    def get_amazon_autocomplete(self, keyword: str, country: str = 'tr', language: str = 'tr') -> List[str]:
        """
        Amazon autocomplete önerilerini çeker

        Args:
            keyword: Aranacak anahtar kelime
            country: Ülke kodu (tr, us, de vb.)
            language: Dil kodu (tr, en vb.)

        Returns:
            Autocomplete önerilerinin listesi
        """
        # Amazon ülke koduna göre doğru domain'i belirle
        domain_map = {
            'tr': 'com.tr',
            'us': 'com',
            'uk': 'co.uk',
            'de': 'de',
            'fr': 'fr',
            'it': 'it',
            'es': 'es',
            'jp': 'co.jp',
            'ca': 'ca'
        }

        domain = domain_map.get(country.lower(), 'com')
        url = f"https://completion.amazon.{domain}/api/2017/suggestions"

        params = {
            'q': keyword,
            'mid': 'ATVPDKIKX0DER',
            'alias': 'aps',
            'fresh': '0',
            'ks': '88',
            'prefix': keyword,
            'event': 'onKeyPress',
            'limit': '11'
        }

        try:
            print(f"Amazon önerileri aranıyor: {keyword}")
            response = requests.get(url, params=params, headers=self.headers)
            response.raise_for_status()

            # Debug için yanıtın ilk kısmını yazdır
            print(f"Amazon API yanıtı (ilk 100 karakter): {response.text[:100]}...")

            try:
                data = response.json()
                suggestions = []

                if 'suggestions' in data and isinstance(data['suggestions'], list):
                    for item in data['suggestions']:
                        if 'value' in item:
                            # HTML etiketlerini temizle
                            suggestion = item['value']
                            # <b> ve </b> etiketlerini kaldır
                            suggestion = suggestion.replace('<b>', '').replace('</b>', '')
                            suggestions.append(suggestion)

                print(f"Bulunan Amazon önerileri: {suggestions}")
                return suggestions

            except json.JSONDecodeError as e:
                print(f"Amazon yanıtı JSON formatında değil: {e}")

                # Hata durumunda boş liste yerine örnek öneriler döndür
                fallback_suggestions = [
                    f"{keyword}",
                    f"{keyword} kitap",
                    f"{keyword} elektronik"
                ]
                print(f"Fallback öneriler döndürülüyor: {fallback_suggestions}")
                return fallback_suggestions

        except requests.RequestException as e:
            print(f"Amazon autocomplete hatası: {e}")
            # Hata durumunda boş liste yerine örnek öneriler döndür
            fallback_suggestions = [
                f"{keyword}",
                f"{keyword} kitap",
                f"{keyword} elektronik"
            ]
            print(f"Hata nedeniyle fallback öneriler döndürülüyor: {fallback_suggestions}")
            return fallback_suggestions

    def get_autocomplete(self, platform: str, keyword: str, country: str = 'tr', language: str = 'tr') -> List[str]:
        """
        Belirtilen platforma göre autocomplete önerilerini çeker

        Args:
            platform: Platform adı ('youtube', 'google', 'amazon')
            keyword: Aranacak anahtar kelime
            country: Ülke kodu (tr, us, de vb.)
            language: Dil kodu (tr, en vb.)

        Returns:
            Autocomplete önerilerinin listesi
        """
        platform = platform.lower()

        try:
            if platform == 'youtube':
                result = self.get_youtube_autocomplete(keyword, country, language)
            elif platform == 'google':
                result = self.get_google_autocomplete(keyword, country, language)
            elif platform == 'amazon':
                result = self.get_amazon_autocomplete(keyword, country, language)
            else:
                print(f"Desteklenmeyen platform: {platform}")
                return []

            # Sonuçları kontrol et
            if not result:
                print(f"Platform {platform} için sonuç bulunamadı: {keyword}")
                # Boş liste yerine örnek sonuçlar döndür
                result = [f"{keyword}", f"{keyword} örnek", f"{keyword} test"]

            # Tam arama için gecikme ekle, hızlı arama için ekleme
            if hasattr(self, 'full_search_mode') and self.full_search_mode:
                print(f"Tam arama modu: {self.delay} saniye bekleniyor...")
                time.sleep(self.delay)
            else:
                print("Hızlı arama modu: Gecikme eklenmedi.")

            return result
        except Exception as e:
            print(f"Autocomplete çekilirken hata: {e}")
            # Hata durumunda örnek sonuçlar döndür
            return [f"{keyword}", f"{keyword} örnek", f"{keyword} test"]

    def get_autocomplete_variations(self, keyword: str, platform: str, country: str = 'tr', language: str = 'tr', full_search: bool = True, status_callback=None) -> List[str]:
        """
        Bir anahtar kelime için farklı varyasyonları çeker

        Args:
            keyword: Anahtar kelime kökü (örn: "bedava" veya "x bedava")
            platform: Platform adı ('youtube', 'google', 'amazon')
            country: Ülke kodu
            language: Dil kodu
            full_search: Tam arama yapılsın mı (True) yoksa sadece ana kelime için mi (False)
            status_callback: Durum güncellemesi için geri çağırma fonksiyonu
        """
        self.full_search_mode = full_search
        print(f"Arama modu: {'Tam' if full_search else 'Hızlı'}")

        all_suggestions = []

        # Eğer kelime "x " ile başlıyorsa, prefix arama modu
        is_prefix_search = keyword.lower().startswith('x ')
        if is_prefix_search:
            # "x " kısmını kaldır
            search_term = keyword[2:].strip()
            # Alfabe ile prefix'leri dene
            alphabet = "abcdefghijklmnopqrstuvwxyz"
            for letter in alphabet:
                if status_callback:
                    status_callback(f"{letter} {search_term} sorgusu için öneriler toplanıyor...")

                variation = f"{letter} {search_term}"
                print(f"Prefix varyasyonu için öneriler çekiliyor: {variation}")
                suggestions = self.get_autocomplete(platform, variation, country, language)

                # Her harf için sadece ilk 5 öneriyi al
                if suggestions:
                    suggestions = suggestions[:5]
                    print(f"{letter} için alınan öneriler: {suggestions}")
                    all_suggestions.extend(suggestions)

                if hasattr(self, 'full_search_mode') and self.full_search_mode:
                    time.sleep(self.delay)
        else:
            # Normal arama modunda devam et
            if status_callback:
                status_callback(f"{keyword} sorgusu için öneriler toplanıyor...")

            base_suggestions = self.get_autocomplete(platform, keyword, country, language)
            all_suggestions.extend(base_suggestions)

            if full_search:
                alphabet = "abcdefghijklmnopqrstuvwxyz"
                for letter in alphabet:
                    if status_callback:
                        status_callback(f"{keyword} {letter} sorgusu için öneriler toplanıyor...")

                    variation = f"{keyword} {letter}"
                    suggestions = self.get_autocomplete(platform, variation, country, language)
                    all_suggestions.extend(suggestions)

                    if hasattr(self, 'full_search_mode') and self.full_search_mode:
                        time.sleep(self.delay)

        # Tekrar eden önerileri kaldır
        unique_suggestions = list(dict.fromkeys(all_suggestions))
        return unique_suggestions


