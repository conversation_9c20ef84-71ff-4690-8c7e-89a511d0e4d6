# Niche Finder - YouTube ve SE<PERSON> <PERSON>htar <PERSON><PERSON> Finder, YouTube arama trendlerini ve SEO açısından değerli anahtar kelimeleri bulmaya yönelik bir masaüstü uygulamasıdır. Bu uygulam<PERSON>, beli<PERSON>i bir anahtar kelime kö<PERSON>, YouTube, Google veya Amazon gibi platformlarda arama kutusunda çıkan otomatik tamamlamaları (autocomplete) çeker ve bunları CSV veya Excel formatında dışa aktarır. Ayrıca Google Trends entegrasyonu ile arama hacimlerini kontrol edip, bu verilerle birlikte raporlar oluşturabilir.

## Özellikler

- YouTube, Google ve Amazon platformlarından autocomplete önerileri çekme
- Google Trends verilerini analiz etme
- Verileri CSV veya Excel formatında dışa aktarma
- Kullanıcı dostu Tkinter arayüzü
- Arama trendlerini grafiklerle görselleştirme
- Farkl<PERSON> ülke ve dil seçenekleri

## Kurulum

1. Python 3.7 veya daha yeni bir sürümü yükleyin.
2. Gerekli kütüphaneleri yükleyin:

```bash
pip install -r requirements.txt
```

## Kullanım

Uygulamayı başlatmak için:

```bash
python main.py
```

### Arama Yapmak İçin:

1. Anahtar kelime kökünü girin (örn: "bedava uc")
2. Platform seçin (YouTube, Google, Amazon)
3. Ülke ve dil seçin
4. Google Trends verilerini dahil etmek istiyorsanız ilgili seçeneği işaretleyin
5. "Arama Yap" butonuna tıklayın
6. Sonuçları görüntüleyin ve istediğinizde "Dışa Aktar" butonuyla CSV veya Excel formatında kaydedin

## Proje Yapısı

- `main.py`: Ana uygulama dosyası
- `gui.py`: Tkinter arayüzü
- `autocomplete_scraper.py`: Autocomplete verilerini çeken modül
- `trends_analyzer.py`: Google Trends verilerini çeken modül
- `data_exporter.py`: Verileri CSV/Excel'e aktaran modül
- `requirements.txt`: Gerekli kütüphaneler

## Notlar

- Her bir autocomplete isteği arası 5 saniye gecikmeli yapılmaktadır (IP engeli riskini azaltmak için).
- Google Trends API'si en fazla 5 anahtar kelime için veri çekebilmektedir.
- Uygulama sadece masaüstü kullanım içindir, web uygulaması değildir.

## Gereksinimler

- Python 3.7+
- requests
- pandas
- pytrends
- openpyxl
- matplotlib
