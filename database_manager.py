import sqlite3
import os
import pandas as pd
from typing import List, Dict, Any, Optional
from datetime import datetime


class DatabaseManager:
    """
    SQLite veritabanı işlemlerini yöneten sınıf
    """

    def __init__(self, db_path: str = "niche_finder.db"):
        """
        Args:
            db_path: Veritabanı dos<PERSON>ının yolu
        """
        self.db_path = db_path
        self.initialize_db()

    def initialize_db(self):
        """Veritabanını ve gerekli tabloları oluşturur"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # Arama geçmişi tablosu
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS search_history (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            keyword TEXT NOT NULL,
            platform TEXT NOT NULL,
            country TEXT NOT NULL,
            language TEXT NOT NULL,
            search_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        ''')

        # Autocomplete sonuçları tablosu
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS autocomplete_results (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            search_id INTEGER NOT NULL,
            suggestion TEXT NOT NULL,
            FOREIGN KEY (search_id) REFERENCES search_history(id) ON DELETE CASCADE
        )
        ''')

        # Google Trends sonuçları tablosu
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS trends_results (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            search_id INTEGER NOT NULL,
            keyword TEXT NOT NULL,
            date TEXT NOT NULL,
            value INTEGER NOT NULL,
            FOREIGN KEY (search_id) REFERENCES search_history(id) ON DELETE CASCADE
        )
        ''')

        # Favoriler tablosu
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS favorites (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            search_id INTEGER NOT NULL,
            suggestion TEXT NOT NULL,
            date_added TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (search_id) REFERENCES search_history(id) ON DELETE CASCADE
        )
        ''')

        conn.commit()
        conn.close()

    def save_search(self, keyword: str, platform: str, country: str, language: str) -> int:
        """
        Yeni bir arama kaydeder ve ID'sini döndürür

        Args:
            keyword: Anahtar kelime
            platform: Platform adı
            country: Ülke kodu
            language: Dil kodu

        Returns:
            Kaydedilen aramanın ID'si
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
        INSERT INTO search_history (keyword, platform, country, language)
        VALUES (?, ?, ?, ?)
        ''', (keyword, platform, country, language))

        search_id = cursor.lastrowid
        conn.commit()
        conn.close()

        return search_id

    def save_autocomplete_results(self, search_id: int, suggestions: List[str]):
        """
        Autocomplete sonuçlarını kaydeder

        Args:
            search_id: Arama ID'si
            suggestions: Autocomplete önerileri listesi
        """
        if not suggestions:
            return

        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # Çoklu ekleme için parametreler
        params = [(search_id, suggestion) for suggestion in suggestions]

        cursor.executemany('''
        INSERT INTO autocomplete_results (search_id, suggestion)
        VALUES (?, ?)
        ''', params)

        conn.commit()
        conn.close()

    def save_trends_results(self, search_id: int, trends_data: Optional[pd.DataFrame]):
        """
        Google Trends sonuçlarını kaydeder

        Args:
            search_id: Arama ID'si
            trends_data: Trends verileri DataFrame olarak
        """
        if trends_data is None or trends_data.empty:
            return

        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        # DataFrame'i işle ve veritabanına kaydet
        for column in trends_data.columns:
            if column != 'isPartial':
                for date, value in trends_data[column].items():
                    date_str = date.strftime('%Y-%m-%d')
                    cursor.execute('''
                    INSERT INTO trends_results (search_id, keyword, date, value)
                    VALUES (?, ?, ?, ?)
                    ''', (search_id, column, date_str, int(value)))

        conn.commit()
        conn.close()

    def get_search_history(self, limit: int = 10) -> List[Dict[str, Any]]:
        """
        Son aramaları getirir

        Args:
            limit: Getirilecek kayıt sayısı

        Returns:
            Arama geçmişi listesi
        """
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row  # Sonuçları sözlük olarak almak için
        cursor = conn.cursor()

        cursor.execute('''
        SELECT id, keyword, platform, country, language, search_date
        FROM search_history
        ORDER BY search_date DESC
        LIMIT ?
        ''', (limit,))

        rows = cursor.fetchall()
        result = [dict(row) for row in rows]

        conn.close()
        return result

    def get_autocomplete_results(self, search_id: int) -> List[str]:
        """
        Belirli bir arama için autocomplete sonuçlarını getirir

        Args:
            search_id: Arama ID'si

        Returns:
            Autocomplete önerileri listesi
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
        SELECT suggestion
        FROM autocomplete_results
        WHERE search_id = ?
        ''', (search_id,))

        rows = cursor.fetchall()
        result = [row[0] for row in rows]

        conn.close()
        return result

    def get_trends_results(self, search_id: int) -> Optional[pd.DataFrame]:
        """
        Belirli bir arama için trends sonuçlarını getirir

        Args:
            search_id: Arama ID'si

        Returns:
            Trends verileri DataFrame olarak
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
        SELECT keyword, date, value
        FROM trends_results
        WHERE search_id = ?
        ''', (search_id,))

        rows = cursor.fetchall()

        if not rows:
            conn.close()
            return None

        # Verileri DataFrame'e dönüştür
        data = {}
        dates = []

        for keyword, date, value in rows:
            if keyword not in data:
                data[keyword] = []

            if date not in dates:
                dates.append(date)

            data[keyword].append((date, value))

        # DataFrame oluştur
        df_data = {}
        for keyword, values in data.items():
            date_value_dict = dict(values)
            df_data[keyword] = [date_value_dict.get(date, 0) for date in sorted(dates)]

        df = pd.DataFrame(df_data, index=sorted(dates))

        conn.close()
        return df

    def delete_search(self, search_id: int) -> bool:
        """
        Belirli bir aramayı ve ilişkili sonuçları siler

        Args:
            search_id: Arama ID'si

        Returns:
            İşlemin başarılı olup olmadığı
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('DELETE FROM search_history WHERE id = ?', (search_id,))

            conn.commit()
            conn.close()
            return True
        except Exception:
            return False

    def add_to_favorites(self, search_id: int, suggestion: str) -> bool:
        """
        Bir arama sonucunu favorilere ekler

        Args:
            search_id: Arama ID'si
            suggestion: Favori olarak eklenecek öneri

        Returns:
            İşlemin başarılı olup olmadığı
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # Önce bu önerinin zaten favorilerde olup olmadığını kontrol et
            cursor.execute('''
            SELECT id FROM favorites
            WHERE search_id = ? AND suggestion = ?
            ''', (search_id, suggestion))

            if cursor.fetchone() is None:  # Eğer yoksa ekle
                cursor.execute('''
                INSERT INTO favorites (search_id, suggestion)
                VALUES (?, ?)
                ''', (search_id, suggestion))

                conn.commit()
                conn.close()
                return True
            else:
                conn.close()
                return False  # Zaten favorilerde
        except Exception as e:
            print(f"Favorilere eklenirken hata: {e}")
            return False

    def remove_from_favorites(self, favorite_id: int) -> bool:
        """
        Bir öğeyi favorilerden kaldırır

        Args:
            favorite_id: Favori ID'si

        Returns:
            İşlemin başarılı olup olmadığı
        """
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute('DELETE FROM favorites WHERE id = ?', (favorite_id,))

            conn.commit()
            conn.close()
            return True
        except Exception as e:
            print(f"Favorilerden kaldırılırken hata: {e}")
            return False

    def get_favorites(self) -> List[Dict[str, Any]]:
        """
        Tüm favorileri getirir

        Returns:
            Favoriler listesi
        """
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row  # Sonuçları sözlük olarak almak için
        cursor = conn.cursor()

        cursor.execute('''
        SELECT f.id, f.search_id, f.suggestion, f.date_added,
               h.keyword, h.platform, h.country
        FROM favorites f
        JOIN search_history h ON f.search_id = h.id
        ORDER BY f.date_added DESC
        ''')

        rows = cursor.fetchall()
        result = [dict(row) for row in rows]

        conn.close()
        return result

    def is_favorite(self, search_id: int, suggestion: str) -> bool:
        """
        Bir önerinin favorilerde olup olmadığını kontrol eder

        Args:
            search_id: Arama ID'si
            suggestion: Kontrol edilecek öneri

        Returns:
            Favorilerde olup olmadığı
        """
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()

        cursor.execute('''
        SELECT id FROM favorites
        WHERE search_id = ? AND suggestion = ?
        ''', (search_id, suggestion))

        result = cursor.fetchone() is not None
        conn.close()
        return result
